package config

import (
	"fmt"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	JWT      JWTConfig
}

type ServerConfig struct {
	Port string
	Host string
}

type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	Charset  string
}

type JWTConfig struct {
	Secret      string
	ExpireHours int
}

func Load() (*Config, error) {
	// Load .env file if it exists
	godotenv.Load()

	expireHours, _ := strconv.Atoi(getEnv("ADMIN_JWT_EXPIRE_HOURS", "24"))

	config := &Config{
		Server: ServerConfig{
			Port: getEnv("ADMIN_SERVER_PORT", "8080"),
			Host: getEnv("ADMIN_SERVER_HOST", "0.0.0.0"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("ADMIN_DATABASE_HOST", "localhost"),
			Port:     getEnv("ADMIN_DATABASE_PORT", "3306"),
			User:     getEnv("ADMIN_DATABASE_USER", "root"),
			Password: getEnv("ADMIN_DATABASE_PASSWORD", "password"),
			DBName:   getEnv("ADMIN_DATABASE_DBNAME", "admin_system"),
			Charset:  getEnv("ADMIN_DATABASE_CHARSET", "utf8mb4"),
		},
		JWT: JWTConfig{
			Secret:      getEnv("ADMIN_JWT_SECRET", "your-super-secret-jwt-key-change-in-production"),
			ExpireHours: expireHours,
		},
	}

	return config, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Server.Port == "" {
		return fmt.Errorf("server port is required")
	}
	if c.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}
	if c.Database.User == "" {
		return fmt.Errorf("database user is required")
	}
	if c.Database.DBName == "" {
		return fmt.Errorf("database name is required")
	}
	if c.JWT.Secret == "" || c.JWT.Secret == "your-super-secret-jwt-key-change-in-production" {
		return fmt.Errorf("JWT secret must be set and changed from default")
	}
	return nil
}

// GetDSN returns the database connection string
func (c *Config) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		c.Database.User,
		c.Database.Password,
		c.Database.Host,
		c.Database.Port,
		c.Database.DBName,
		c.Database.Charset,
	)
}

// PrintConfig prints the current configuration (without sensitive data)
func (c *Config) PrintConfig() {
	fmt.Println("=== Admin System Configuration ===")
	fmt.Printf("Server: %s:%s\n", c.Server.Host, c.Server.Port)
	fmt.Printf("Database: %s@%s:%s/%s\n", c.Database.User, c.Database.Host, c.Database.Port, c.Database.DBName)
	fmt.Printf("JWT Expire Hours: %d\n", c.JWT.ExpireHours)
	fmt.Println("===================================")
}
