package database

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"xiaoxingcloud.com/admin/internal/auth"
	"xiaoxingcloud.com/admin/internal/config"
	"xiaoxingcloud.com/admin/internal/models"
)

var DB *gorm.DB

// Connect initializes the database connection
func Connect(cfg *config.Config) error {
	dsn := cfg.GetDSN()

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	log.Println("Database connected successfully")
	return nil
}

// Migrate runs database migrations
func Migrate() error {
	err := DB.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
		&models.Attribute{},
		&models.Policy{},
	)

	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	log.Println("Database migration completed successfully")
	return nil
}

// Seed creates initial data
func Seed() error {
	// Create default permissions
	permissions := []models.Permission{
		{Name: "users.create", Resource: "users", Action: "create", Description: "Create new users"},
		{Name: "users.read", Resource: "users", Action: "read", Description: "View users"},
		{Name: "users.update", Resource: "users", Action: "update", Description: "Update users"},
		{Name: "users.delete", Resource: "users", Action: "delete", Description: "Delete users"},
		{Name: "roles.create", Resource: "roles", Action: "create", Description: "Create new roles"},
		{Name: "roles.read", Resource: "roles", Action: "read", Description: "View roles"},
		{Name: "roles.update", Resource: "roles", Action: "update", Description: "Update roles"},
		{Name: "roles.delete", Resource: "roles", Action: "delete", Description: "Delete roles"},
		{Name: "permissions.read", Resource: "permissions", Action: "read", Description: "View permissions"},
		{Name: "system.admin", Resource: "system", Action: "admin", Description: "Full system administration"},
	}

	for _, permission := range permissions {
		var existingPermission models.Permission
		if err := DB.Where("name = ?", permission.Name).First(&existingPermission).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&permission).Error; err != nil {
					return fmt.Errorf("failed to create permission %s: %w", permission.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check permission %s: %w", permission.Name, err)
			}
		}
	}

	// Create default roles
	roles := []models.Role{
		{Name: "admin", Description: "System Administrator with full access"},
		{Name: "manager", Description: "Manager with user management access"},
		{Name: "user", Description: "Regular user with limited access"},
	}

	for _, role := range roles {
		var existingRole models.Role
		if err := DB.Where("name = ?", role.Name).First(&existingRole).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&role).Error; err != nil {
					return fmt.Errorf("failed to create role %s: %w", role.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check role %s: %w", role.Name, err)
			}
		}
	}

	// Assign permissions to admin role
	var adminRole models.Role
	if err := DB.Where("name = ?", "admin").First(&adminRole).Error; err != nil {
		return fmt.Errorf("failed to find admin role: %w", err)
	}

	var allPermissions []models.Permission
	if err := DB.Find(&allPermissions).Error; err != nil {
		return fmt.Errorf("failed to fetch permissions: %w", err)
	}

	// Associate all permissions with admin role
	if err := DB.Model(&adminRole).Association("Permissions").Replace(allPermissions); err != nil {
		return fmt.Errorf("failed to assign permissions to admin role: %w", err)
	}

	// Create default admin user
	var existingAdmin models.User
	if err := DB.Where("username = ?", "admin").First(&existingAdmin).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Hash the default password
			hashedPassword, err := auth.HashPassword("Admin123!")
			if err != nil {
				return fmt.Errorf("failed to hash admin password: %w", err)
			}

			// Create admin user
			adminUser := models.User{
				Username:  "admin",
				Email:     "<EMAIL>",
				Password:  hashedPassword,
				FirstName: "System",
				LastName:  "Administrator",
				IsActive:  true,
			}

			if err := DB.Create(&adminUser).Error; err != nil {
				return fmt.Errorf("failed to create admin user: %w", err)
			}

			// Assign admin role to admin user
			userRole := models.UserRole{
				UserID: adminUser.ID,
				RoleID: adminRole.ID,
			}

			if err := DB.Create(&userRole).Error; err != nil {
				return fmt.Errorf("failed to assign admin role to admin user: %w", err)
			}

			log.Println("Default admin user created successfully (username: admin, password: Admin123!)")
		} else {
			return fmt.Errorf("failed to check admin user: %w", err)
		}
	} else {
		log.Println("Admin user already exists, skipping creation")
	}

	log.Println("Database seeding completed successfully")
	return nil
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return DB
}
