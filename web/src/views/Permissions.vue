<template>
  <Layout>
    <div>
      <!-- Header -->
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-xl font-semibold text-gray-900">Permission Management</h1>
          <p class="mt-2 text-sm text-gray-700">
            Manage role permissions and user role assignments.
          </p>
        </div>
      </div>

      <!-- Tabs -->
      <div class="mt-6">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <button
              @click="activeTab = 'role-permissions'"
              :class="activeTab === 'role-permissions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
              class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            >
              Role Permissions
            </button>
            <button
              @click="activeTab = 'user-roles'"
              :class="activeTab === 'user-roles' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
              class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            >
              User Roles
            </button>
          </nav>
        </div>
      </div>

      <!-- Role Permissions Tab -->
      <div v-if="activeTab === 'role-permissions'" class="mt-6">
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              Role Permissions Matrix
            </h3>
            
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th 
                      v-for="permission in permissions" 
                      :key="permission.id"
                      class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      <div class="transform -rotate-45 origin-left">
                        {{ permission.name }}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="role in roles" :key="role.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {{ role.name }}
                    </td>
                    <td 
                      v-for="permission in permissions" 
                      :key="permission.id"
                      class="px-3 py-4 whitespace-nowrap text-center"
                    >
                      <input
                        type="checkbox"
                        :checked="hasPermission(role, permission)"
                        @change="togglePermission(role, permission, $event)"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- User Roles Tab -->
      <div v-if="activeTab === 'user-roles'" class="mt-6">
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              User Role Assignments
            </h3>
            
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Current Roles
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="user in users" :key="user.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div>
                          <div class="text-sm font-medium text-gray-900">
                            {{ user.first_name }} {{ user.last_name }}
                          </div>
                          <div class="text-sm text-gray-500">
                            {{ user.email }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex flex-wrap gap-1">
                        <span
                          v-for="role in user.roles"
                          :key="role.id"
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {{ role.name }}
                        </span>
                        <span v-if="!user.roles?.length" class="text-gray-400 text-sm">
                          No roles assigned
                        </span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        @click="openUserRoleModal(user)"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        Manage Roles
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Role Assignment Modal -->
    <div v-if="showUserRoleModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeUserRoleModal"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Manage Roles for {{ selectedUser?.first_name }} {{ selectedUser?.last_name }}
                </h3>
                
                <div class="space-y-3">
                  <div v-for="role in roles" :key="role.id" class="flex items-center">
                    <input
                      :id="`role-${role.id}`"
                      type="checkbox"
                      :checked="userHasRole(selectedUser, role)"
                      @change="toggleUserRole(selectedUser, role, $event)"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`role-${role.id}`" class="ml-3 text-sm text-gray-700">
                      <span class="font-medium">{{ role.name }}</span>
                      <span class="text-gray-500 block">{{ role.description }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="closeUserRoleModal"
              class="btn btn-primary sm:ml-3 sm:w-auto w-full"
            >
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import { useAuthStore } from '../stores/auth'
import { useUsersStore } from '../stores/users'

const authStore = useAuthStore()
const usersStore = useUsersStore()

const activeTab = ref('role-permissions')
const showUserRoleModal = ref(false)
const selectedUser = ref<any>(null)

// Mock data - replace with actual API calls
const roles = ref([
  { id: 1, name: 'admin', description: 'System Administrator', permissions: [] },
  { id: 2, name: 'manager', description: 'Manager', permissions: [] },
  { id: 3, name: 'user', description: 'Regular User', permissions: [] }
])

const permissions = ref([
  { id: 1, name: 'users.create', resource: 'users', action: 'create' },
  { id: 2, name: 'users.read', resource: 'users', action: 'read' },
  { id: 3, name: 'users.update', resource: 'users', action: 'update' },
  { id: 4, name: 'users.delete', resource: 'users', action: 'delete' },
  { id: 5, name: 'roles.create', resource: 'roles', action: 'create' },
  { id: 6, name: 'roles.read', resource: 'roles', action: 'read' },
  { id: 7, name: 'roles.update', resource: 'roles', action: 'update' },
  { id: 8, name: 'roles.delete', resource: 'roles', action: 'delete' }
])

const users = ref(usersStore.users)

const hasPermission = (role: any, permission: any) => {
  return role.permissions?.some((p: any) => p.id === permission.id) || false
}

const togglePermission = async (role: any, permission: any, event: any) => {
  const isChecked = event.target.checked
  console.log(`Toggle permission ${permission.name} for role ${role.name}: ${isChecked}`)
  // TODO: Implement API call to assign/remove permission
}

const userHasRole = (user: any, role: any) => {
  return user?.roles?.some((r: any) => r.id === role.id) || false
}

const toggleUserRole = async (user: any, role: any, event: any) => {
  const isChecked = event.target.checked
  try {
    if (isChecked) {
      await usersStore.assignRole(user.id, role.id)
    } else {
      await usersStore.removeRole(user.id, role.id)
    }
    // Refresh user data
    await usersStore.fetchUsers()
  } catch (error) {
    console.error('Error toggling user role:', error)
    // Revert checkbox state on error
    event.target.checked = !isChecked
  }
}

const openUserRoleModal = (user: any) => {
  selectedUser.value = user
  showUserRoleModal.value = true
}

const closeUserRoleModal = () => {
  showUserRoleModal.value = false
  selectedUser.value = null
}

onMounted(() => {
  usersStore.fetchUsers()
  // TODO: Fetch roles and permissions from API
})
</script>
