<template>
  <Layout>
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-xl font-semibold text-gray-900">Users</h1>
          <p class="mt-2 text-sm text-gray-700">
            A list of all users in the system including their name, email, and role.
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            v-if="authStore.hasPermission('users.create')"
            @click="openCreateModal"
            type="button"
            class="btn btn-primary"
          >
            Add user
          </button>
        </div>
      </div>

      <!-- Users Table -->
      <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Email
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Roles
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Created
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="user in usersStore.users" :key="user.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {{ user.first_name }} {{ user.last_name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ user.email }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span v-if="user.roles?.length" class="space-x-1">
                        <span
                          v-for="role in user.roles"
                          :key="role.id"
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {{ role.name }}
                        </span>
                      </span>
                      <span v-else class="text-gray-400">No roles</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        :class="user.is_active ? 'text-green-800 bg-green-100' : 'text-red-800 bg-red-100'"
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      >
                        {{ user.is_active ? 'Active' : 'Inactive' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDate(user.created_at) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        v-if="authStore.hasPermission('users.update')"
                        @click="editUser(user)"
                        class="text-blue-600 hover:text-blue-900 mr-4"
                      >
                        Edit
                      </button>
                      <button
                        v-if="authStore.hasPermission('users.delete')"
                        @click="deleteUser(user)"
                        class="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="usersStore.total > 0" class="mt-6 flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Showing {{ ((usersStore.currentPage - 1) * usersStore.pageSize) + 1 }} to
          {{ Math.min(usersStore.currentPage * usersStore.pageSize, usersStore.total) }} of
          {{ usersStore.total }} results
        </div>
        <div class="flex space-x-2">
          <button
            @click="usersStore.previousPage()"
            :disabled="!usersStore.hasPreviousPage"
            class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            @click="usersStore.nextPage()"
            :disabled="!usersStore.hasNextPage"
            class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    </div>

    <!-- Create/Edit User Modal -->
    <div v-if="showUserModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form @submit.prevent="submitUser">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    {{ editingUser ? 'Edit User' : 'Create New User' }}
                  </h3>

                  <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <label class="form-label">First Name</label>
                        <input
                          v-model="userForm.first_name"
                          type="text"
                          required
                          class="form-input"
                        />
                      </div>
                      <div>
                        <label class="form-label">Last Name</label>
                        <input
                          v-model="userForm.last_name"
                          type="text"
                          required
                          class="form-input"
                        />
                      </div>
                    </div>

                    <div>
                      <label class="form-label">Username</label>
                      <input
                        v-model="userForm.username"
                        type="text"
                        required
                        class="form-input"
                      />
                    </div>

                    <div>
                      <label class="form-label">Email</label>
                      <input
                        v-model="userForm.email"
                        type="email"
                        required
                        class="form-input"
                      />
                    </div>

                    <div v-if="!editingUser">
                      <label class="form-label">Password</label>
                      <input
                        v-model="userForm.password"
                        type="password"
                        required
                        class="form-input"
                        placeholder="Minimum 8 characters with uppercase, lowercase, number and special character"
                      />
                    </div>

                    <div>
                      <label class="form-label">Status</label>
                      <select v-model="userForm.is_active" class="form-input">
                        <option :value="true">Active</option>
                        <option :value="false">Inactive</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                :disabled="usersStore.loading"
                class="btn btn-primary sm:ml-3 sm:w-auto w-full"
              >
                {{ usersStore.loading ? 'Saving...' : (editingUser ? 'Update User' : 'Create User') }}
              </button>
              <button
                type="button"
                @click="closeModal"
                class="btn btn-secondary mt-3 sm:mt-0 sm:w-auto w-full"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import { useAuthStore } from '../stores/auth'
import { useUsersStore } from '../stores/users'

const authStore = useAuthStore()
const usersStore = useUsersStore()
const showUserModal = ref(false)
const editingUser = ref<any>(null)

const userForm = reactive({
  username: '',
  email: '',
  password: '',
  first_name: '',
  last_name: '',
  is_active: true
})

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const openCreateModal = () => {
  editingUser.value = null
  resetForm()
  showUserModal.value = true
}

const editUser = (user: any) => {
  editingUser.value = user
  userForm.username = user.username
  userForm.email = user.email
  userForm.first_name = user.first_name
  userForm.last_name = user.last_name
  userForm.is_active = user.is_active
  userForm.password = '' // Don't populate password for editing
  showUserModal.value = true
}

const closeModal = () => {
  showUserModal.value = false
  editingUser.value = null
  resetForm()
}

const resetForm = () => {
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.first_name = ''
  userForm.last_name = ''
  userForm.is_active = true
}

const submitUser = async () => {
  try {
    if (editingUser.value) {
      // Update existing user
      const updateData: any = {
        username: userForm.username,
        email: userForm.email,
        first_name: userForm.first_name,
        last_name: userForm.last_name,
        is_active: userForm.is_active
      }

      await usersStore.updateUser(editingUser.value.id, updateData)
    } else {
      // Create new user
      await usersStore.createUser(userForm)
    }

    closeModal()
    await usersStore.fetchUsers()
  } catch (error) {
    console.error('Error saving user:', error)
  }
}

const deleteUser = async (user: any) => {
  if (confirm(`Are you sure you want to delete ${user.first_name} ${user.last_name}?`)) {
    try {
      await usersStore.deleteUser(user.id)
      await usersStore.fetchUsers()
    } catch (error) {
      console.error('Error deleting user:', error)
    }
  }
}

// Update the create button click handler
const showCreateModal = ref(false) // Keep for backward compatibility
const handleCreateClick = () => {
  openCreateModal()
}

onMounted(() => {
  usersStore.fetchUsers()
})
</script>
